// Zustand store for application state management

import { create } from 'zustand';
import { AppState, DownloadProgress, ToastMessage } from '../types';
import apiService from '../services/api';
import websocketService from '../services/websocket';

// Generate unique ID for toasts
const generateId = () => Math.random().toString(36).substr(2, 9);

export const useAppStore = create<AppState>((set, get) => ({
  // Initial state
  systemInfo: null,
  isLoading: false,
  licenseKey: '',
  licenseValid: false,
  appInfo: null,
  downloadProgress: null,
  isDownloading: false,
  toasts: [],

  // Actions
  setLicenseKey: (key: string) => {
    set({ licenseKey: key, licenseValid: false, appInfo: null });
  },

  validateLicense: async () => {
    const { licenseKey } = get();
    
    if (!licenseKey.trim()) {
      get().addToast({
        type: 'error',
        title: 'Validation Error',
        message: 'Please enter a license key'
      });
      return;
    }

    set({ isLoading: true });

    try {
      const response = await apiService.validateLicense(licenseKey);
      
      if (response.is_valid && response.app_info) {
        set({ 
          licenseValid: true, 
          appInfo: response.app_info,
          isLoading: false 
        });
        
        get().addToast({
          type: 'success',
          title: 'License Valid',
          message: `Found app: ${response.app_info.app_name}`
        });
      } else {
        set({ 
          licenseValid: false, 
          appInfo: null,
          isLoading: false 
        });
        
        get().addToast({
          type: 'error',
          title: 'Invalid License',
          message: response.error_message || 'License key is not valid'
        });
      }
    } catch (error) {
      set({ 
        licenseValid: false, 
        appInfo: null,
        isLoading: false 
      });
      
      get().addToast({
        type: 'error',
        title: 'Validation Failed',
        message: error instanceof Error ? error.message : 'An error occurred'
      });
    }
  },

  startDownload: async () => {
    const { licenseKey, appInfo, systemInfo } = get();
    
    if (!licenseKey || !appInfo) {
      get().addToast({
        type: 'error',
        title: 'Download Error',
        message: 'Please validate your license key first'
      });
      return;
    }

    if (!systemInfo?.steam_path) {
      // Try to detect Steam path first
      try {
        const steamResponse = await apiService.detectSteamPath();
        if (!steamResponse.is_valid || !steamResponse.steam_path) {
          get().addToast({
            type: 'error',
            title: 'Steam Not Found',
            message: 'Steam installation not found. Please install Steam first.'
          });
          return;
        }
        
        // Update system info with detected Steam path
        await get().updateSystemInfo();
      } catch (error) {
        get().addToast({
          type: 'error',
          title: 'Steam Detection Failed',
          message: 'Could not detect Steam installation'
        });
        return;
      }
    }

    // Check if Steam is running and offer to close it
    if (systemInfo?.steam_running) {
      const shouldClose = window.confirm(
        'Steam is currently running. It needs to be closed for the installation to work properly.\n\nClose Steam now?'
      );
      
      if (shouldClose) {
        try {
          await apiService.closeSteamProcesses();
          get().addToast({
            type: 'success',
            title: 'Steam Closed',
            message: 'Steam processes have been closed'
          });
          
          // Update system info
          await get().updateSystemInfo();
        } catch (error) {
          get().addToast({
            type: 'error',
            title: 'Failed to Close Steam',
            message: 'Could not close Steam processes'
          });
          return;
        }
      } else {
        get().addToast({
          type: 'warning',
          title: 'Download Cancelled',
          message: 'Please close Steam manually and try again'
        });
        return;
      }
    }

    set({ isDownloading: true, downloadProgress: null });

    try {
      const response = await apiService.startDownload(
        licenseKey, 
        appInfo, 
        systemInfo!.steam_path!
      );
      
      if (response.success) {
        get().addToast({
          type: 'success',
          title: 'Download Started',
          message: 'File download has begun'
        });
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      set({ isDownloading: false, downloadProgress: null });
      
      get().addToast({
        type: 'error',
        title: 'Download Failed',
        message: error instanceof Error ? error.message : 'Failed to start download'
      });
    }
  },

  updateSystemInfo: async () => {
    try {
      const systemInfo = await apiService.getSystemInfo();
      set({ systemInfo });
    } catch (error) {
      console.error('Failed to update system info:', error);
    }
  },

  setDownloadProgress: (progress: DownloadProgress | null) => {
    set({ downloadProgress: progress });
    
    // If download is completed or failed, update downloading state
    if (progress?.status === 'completed' || progress?.status === 'error') {
      set({ isDownloading: false });
    }
  },

  addToast: (toast: Omit<ToastMessage, 'id'>) => {
    const newToast: ToastMessage = {
      ...toast,
      id: generateId(),
      duration: toast.duration || 5000
    };
    
    set(state => ({
      toasts: [...state.toasts, newToast]
    }));

    // Auto-remove toast after duration
    setTimeout(() => {
      get().removeToast(newToast.id);
    }, newToast.duration);
  },

  removeToast: (id: string) => {
    set(state => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  }
}));

// Initialize WebSocket connection and system info on store creation
const initializeApp = async () => {
  const store = useAppStore.getState();
  
  // Connect WebSocket
  const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  websocketService.connect(clientId);
  
  // Listen for download progress updates
  websocketService.onMessage((message) => {
    if (message.type === 'download_progress') {
      store.setDownloadProgress(message as DownloadProgress);
    }
  });
  
  // Load initial system info
  await store.updateSystemInfo();
};

// Initialize when the module is loaded
initializeApp().catch(console.error);

export default useAppStore;
