@echo off
REM Steam Tools v3 - Build Portable Executable

echo ========================================
echo Steam Tools v3 - Portable Build
echo ========================================
echo.

echo Building portable executable...
echo.

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

REM Build frontend
echo Step 1: Building React frontend...
cd frontend
npm run build
if errorlevel 1 (
    echo ERROR: Frontend build failed
    cd ..
    pause
    exit /b 1
)
cd ..
echo Frontend build completed
echo.

REM Build portable executable
echo Step 2: Building portable executable...
cd electron

REM Kill any running Steam Tools processes to avoid file locks
echo Stopping any running Steam Tools processes...
taskkill /F /IM "Steam Tools v3.exe" 2>nul || echo No Steam Tools processes running

REM Clean dist directory with retry logic
echo Cleaning previous build...
for /L %%i in (1,1,3) do (
    rmdir /s /q "..\dist" 2>nul && goto :build_success
    echo Attempt %%i failed, retrying in 2 seconds...
    timeout /t 2 >nul
)

:build_success
npm run build-dir
if errorlevel 1 (
    echo ERROR: Electron build failed
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo BUILD COMPLETED SUCCESSFULLY!
echo.
echo Your executable is ready at:
echo dist\win-unpacked\Steam Tools v3.exe
echo.
echo Just double-click to run - no installation needed!
echo.

pause
