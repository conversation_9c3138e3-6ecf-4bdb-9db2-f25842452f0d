@echo off
REM Steam Tools v3 - Build Portable Executable

echo ========================================
echo Steam Tools v3 - Portable Build
echo ========================================
echo.

echo Building portable executable...
echo.

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

REM Build frontend
echo Step 1: Building React frontend...
cd frontend
npm run build
if errorlevel 1 (
    echo ERROR: Frontend build failed
    cd ..
    pause
    exit /b 1
)
cd ..
echo Frontend build completed
echo.

REM Build portable executable
echo Step 2: Building portable executable...
cd electron
npm run build-dir
if errorlevel 1 (
    echo ERROR: Electron build failed
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo BUILD COMPLETED SUCCESSFULLY!
echo.
echo Your executable is ready at:
echo dist\win-unpacked\Steam Tools v3.exe
echo.
echo Just double-click to run - no installation needed!
echo.

pause
