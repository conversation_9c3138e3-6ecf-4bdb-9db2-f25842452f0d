"""Configuration service for managing app settings and data."""

import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, List

from models.steam import ConfigData, AppInfo, LicenseHistoryEntry
from config.settings import settings

logger = logging.getLogger(__name__)


class ConfigService:
    """Service for managing application configuration."""
    
    def __init__(self):
        self.config_file = settings.config_file
        self.config_data: ConfigData = self._load_config()
    
    def _load_config(self) -> ConfigData:
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return ConfigData(**data)
            else:
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> ConfigData:
        """Get default configuration."""
        return ConfigData(
            steam_path="",
            license_key_history=[],
            admin_settings={
                "password_hash": settings.admin_password_hash,
                "auto_detect_steam": True,
                "remember_license_keys": True,
                "max_history_entries": 50
            },
            app_cache={},
            last_used={
                "license_key": "",
                "app_info": {}
            }
        )
    
    async def save_config(self) -> None:
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(
                    self.config_data.model_dump(),
                    f,
                    indent=2,
                    ensure_ascii=False,
                    default=str
                )
            logger.info("Configuration saved successfully")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
            raise
    
    async def get_config(self) -> Dict[str, Any]:
        """Get current configuration."""
        return self.config_data.model_dump()
    
    async def set_steam_path(self, steam_path: str) -> None:
        """Set Steam installation path."""
        self.config_data.steam_path = steam_path
        await self.save_config()
        logger.info(f"Steam path updated: {steam_path}")
    
    async def get_steam_path(self) -> str:
        """Get Steam installation path."""
        return self.config_data.steam_path
    
    async def add_license_history(self, license_key: str, app_info: AppInfo, success: bool) -> None:
        """Add license key to history."""
        try:
            history_entry = {
                "key": license_key,
                "app_name": app_info.app_name,
                "app_id": app_info.app_id,
                "license_prefix": app_info.license_prefix,
                "timestamp": datetime.now().isoformat(),
                "success": success
            }
            
            # Add to beginning of list
            self.config_data.license_key_history.insert(0, history_entry)
            
            # Limit history entries
            max_entries = self.config_data.admin_settings.get("max_history_entries", 50)
            if len(self.config_data.license_key_history) > max_entries:
                self.config_data.license_key_history = self.config_data.license_key_history[:max_entries]
            
            await self.save_config()
            logger.info(f"Added license history entry for {app_info.app_name}")
            
        except Exception as e:
            logger.error(f"Error adding license history: {e}")
    
    async def get_license_history(self) -> List[Dict[str, Any]]:
        """Get license key history."""
        return self.config_data.license_key_history
    
    async def clear_license_history(self) -> None:
        """Clear license key history."""
        self.config_data.license_key_history = []
        await self.save_config()
        logger.info("License history cleared")
    
    async def update_admin_settings(self, settings_update: Dict[str, Any]) -> None:
        """Update admin settings."""
        self.config_data.admin_settings.update(settings_update)
        await self.save_config()
        logger.info("Admin settings updated")
    
    async def get_admin_settings(self) -> Dict[str, Any]:
        """Get admin settings."""
        return self.config_data.admin_settings
    
    async def cache_app_data(self, app_id: str, app_data: Dict[str, Any]) -> None:
        """Cache app data for faster access."""
        self.config_data.app_cache[app_id] = {
            "data": app_data,
            "cached_at": datetime.now().isoformat()
        }
        await self.save_config()
    
    async def get_cached_app_data(self, app_id: str) -> Dict[str, Any]:
        """Get cached app data."""
        return self.config_data.app_cache.get(app_id, {})
    
    async def set_last_used(self, license_key: str, app_info: Dict[str, Any]) -> None:
        """Set last used license key and app info."""
        self.config_data.last_used = {
            "license_key": license_key,
            "app_info": app_info,
            "timestamp": datetime.now().isoformat()
        }
        await self.save_config()
    
    async def get_last_used(self) -> Dict[str, Any]:
        """Get last used license key and app info."""
        return self.config_data.last_used
    
    async def reset_config(self) -> None:
        """Reset configuration to defaults."""
        self.config_data = self._get_default_config()
        await self.save_config()
        logger.info("Configuration reset to defaults")
    
    async def export_config(self) -> str:
        """Export configuration as JSON string."""
        return json.dumps(
            self.config_data.model_dump(),
            indent=2,
            ensure_ascii=False,
            default=str
        )
    
    async def import_config(self, config_json: str) -> None:
        """Import configuration from JSON string."""
        try:
            data = json.loads(config_json)
            self.config_data = ConfigData(**data)
            await self.save_config()
            logger.info("Configuration imported successfully")
        except Exception as e:
            logger.error(f"Error importing config: {e}")
            raise
