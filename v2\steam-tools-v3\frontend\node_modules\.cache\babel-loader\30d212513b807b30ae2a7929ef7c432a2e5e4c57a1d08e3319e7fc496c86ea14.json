{"ast": null, "code": "// License input component with validation and formatting\nimport React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LicenseInput=_ref=>{let{value,onChange,onSubmit,disabled=false,error=false,success=false}=_ref;const[focused,setFocused]=useState(false);// Format license key input (XXXX-XXXX-XXXX-XXXX)\nconst formatLicenseKey=input=>{// Remove all non-alphanumeric characters and convert to uppercase\nconst cleaned=input.replace(/[^A-Za-z0-9]/g,'').toUpperCase();// Split into groups of 4 characters\nconst groups=cleaned.match(/.{1,4}/g)||[];// Join with dashes and limit to 19 characters (4-4-4-4 + 3 dashes)\nconst formatted=groups.join('-');return formatted.length>19?formatted.substring(0,19):formatted;};// Validate license key format\nconst isValidFormat=key=>{return /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(key);};const handleInputChange=e=>{const formatted=formatLicenseKey(e.target.value);onChange(formatted);};const handleKeyPress=e=>{if(e.key==='Enter'&&!disabled){onSubmit();}};const inputClasses=['input-steam',error&&'error',success&&'success',focused&&'ring-2 ring-steam-primary ring-opacity-50'].filter(Boolean).join(' ');return/*#__PURE__*/_jsxs(\"div\",{className:\"license-section\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-steam-foreground mb-2\",children:\"License Key\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:value,onChange:handleInputChange,onKeyPress:handleKeyPress,onFocus:()=>setFocused(true),onBlur:()=>setFocused(false),disabled:disabled,placeholder:\"XXXX-XXXX-XXXX-XXXX\",maxLength:19,className:inputClasses,autoComplete:\"off\",spellCheck:false}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-2 flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-2\",children:value&&/*#__PURE__*/_jsx(_Fragment,{children:isValidFormat(value)?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-steam-accent\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4 mr-1\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",clipRule:\"evenodd\"})}),\"Valid format\"]}):value.length===19?/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-steam-destructive\",children:[/*#__PURE__*/_jsx(\"svg\",{className:\"w-4 h-4 mr-1\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",clipRule:\"evenodd\"})}),\"Invalid format\"]}):null})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-steam-muted-foreground\",children:[value.length,\"/19\"]})]})]});};export default LicenseInput;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "LicenseInput", "_ref", "value", "onChange", "onSubmit", "disabled", "error", "success", "focused", "setFocused", "formatLicenseKey", "input", "cleaned", "replace", "toUpperCase", "groups", "match", "formatted", "join", "length", "substring", "isValidFormat", "key", "test", "handleInputChange", "e", "target", "handleKeyPress", "inputClasses", "filter", "Boolean", "className", "children", "type", "onKeyPress", "onFocus", "onBlur", "placeholder", "max<PERSON><PERSON><PERSON>", "autoComplete", "spell<PERSON>heck", "fill", "viewBox", "fillRule", "d", "clipRule"], "sources": ["D:/SourceCode/SteamManifestUpdater/v2/steam-tools-v3/frontend/src/components/LicenseInput.tsx"], "sourcesContent": ["// License input component with validation and formatting\n\nimport React, { useState } from 'react';\nimport { LicenseInputProps } from '../types';\n\nconst LicenseInput: React.FC<LicenseInputProps> = ({\n  value,\n  onChange,\n  onSubmit,\n  disabled = false,\n  error = false,\n  success = false\n}) => {\n  const [focused, setFocused] = useState(false);\n\n  // Format license key input (XXXX-XXXX-XXXX-XXXX)\n  const formatLicenseKey = (input: string): string => {\n    // Remove all non-alphanumeric characters and convert to uppercase\n    const cleaned = input.replace(/[^A-Za-z0-9]/g, '').toUpperCase();\n    \n    // Split into groups of 4 characters\n    const groups = cleaned.match(/.{1,4}/g) || [];\n    \n    // Join with dashes and limit to 19 characters (4-4-4-4 + 3 dashes)\n    const formatted = groups.join('-');\n    return formatted.length > 19 ? formatted.substring(0, 19) : formatted;\n  };\n\n  // Validate license key format\n  const isValidFormat = (key: string): boolean => {\n    return /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(key);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const formatted = formatLicenseKey(e.target.value);\n    onChange(formatted);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter' && !disabled) {\n      onSubmit();\n    }\n  };\n\n  const inputClasses = [\n    'input-steam',\n    error && 'error',\n    success && 'success',\n    focused && 'ring-2 ring-steam-primary ring-opacity-50'\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className=\"license-section\">\n      <label className=\"block text-sm font-medium text-steam-foreground mb-2\">\n        License Key\n      </label>\n      <input\n        type=\"text\"\n        value={value}\n        onChange={handleInputChange}\n        onKeyPress={handleKeyPress}\n        onFocus={() => setFocused(true)}\n        onBlur={() => setFocused(false)}\n        disabled={disabled}\n        placeholder=\"XXXX-XXXX-XXXX-XXXX\"\n        maxLength={19}\n        className={inputClasses}\n        autoComplete=\"off\"\n        spellCheck={false}\n      />\n      \n      {/* Validation indicator */}\n      <div className=\"mt-2 flex items-center justify-between text-sm\">\n        <div className=\"flex items-center space-x-2\">\n          {value && (\n            <>\n              {isValidFormat(value) ? (\n                <div className=\"flex items-center text-steam-accent\">\n                  <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Valid format\n                </div>\n              ) : value.length === 19 ? (\n                <div className=\"flex items-center text-steam-destructive\">\n                  <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Invalid format\n                </div>\n              ) : null}\n            </>\n          )}\n        </div>\n        \n        <div className=\"text-steam-muted-foreground\">\n          {value.length}/19\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LicenseInput;\n"], "mappings": "AAAA;AAEA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGxC,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAO5C,IAP6C,CACjDC,KAAK,CACLC,QAAQ,CACRC,QAAQ,CACRC,QAAQ,CAAG,KAAK,CAChBC,KAAK,CAAG,KAAK,CACbC,OAAO,CAAG,KACZ,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAE7C;AACA,KAAM,CAAAiB,gBAAgB,CAAIC,KAAa,EAAa,CAClD;AACA,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACE,OAAO,CAAC,eAAe,CAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAEhE;AACA,KAAM,CAAAC,MAAM,CAAGH,OAAO,CAACI,KAAK,CAAC,SAAS,CAAC,EAAI,EAAE,CAE7C;AACA,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,IAAI,CAAC,GAAG,CAAC,CAClC,MAAO,CAAAD,SAAS,CAACE,MAAM,CAAG,EAAE,CAAGF,SAAS,CAACG,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAGH,SAAS,CACvE,CAAC,CAED;AACA,KAAM,CAAAI,aAAa,CAAIC,GAAW,EAAc,CAC9C,MAAO,oDAAmD,CAACC,IAAI,CAACD,GAAG,CAAC,CACtE,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAAR,SAAS,CAAGP,gBAAgB,CAACe,CAAC,CAACC,MAAM,CAACxB,KAAK,CAAC,CAClDC,QAAQ,CAACc,SAAS,CAAC,CACrB,CAAC,CAED,KAAM,CAAAU,cAAc,CAAIF,CAAwC,EAAK,CACnE,GAAIA,CAAC,CAACH,GAAG,GAAK,OAAO,EAAI,CAACjB,QAAQ,CAAE,CAClCD,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAED,KAAM,CAAAwB,YAAY,CAAG,CACnB,aAAa,CACbtB,KAAK,EAAI,OAAO,CAChBC,OAAO,EAAI,SAAS,CACpBC,OAAO,EAAI,2CAA2C,CACvD,CAACqB,MAAM,CAACC,OAAO,CAAC,CAACZ,IAAI,CAAC,GAAG,CAAC,CAE3B,mBACErB,KAAA,QAAKkC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BrC,IAAA,UAAOoC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAAC,aAExE,CAAO,CAAC,cACRrC,IAAA,UACEsC,IAAI,CAAC,MAAM,CACX/B,KAAK,CAAEA,KAAM,CACbC,QAAQ,CAAEqB,iBAAkB,CAC5BU,UAAU,CAAEP,cAAe,CAC3BQ,OAAO,CAAEA,CAAA,GAAM1B,UAAU,CAAC,IAAI,CAAE,CAChC2B,MAAM,CAAEA,CAAA,GAAM3B,UAAU,CAAC,KAAK,CAAE,CAChCJ,QAAQ,CAAEA,QAAS,CACnBgC,WAAW,CAAC,qBAAqB,CACjCC,SAAS,CAAE,EAAG,CACdP,SAAS,CAAEH,YAAa,CACxBW,YAAY,CAAC,KAAK,CAClBC,UAAU,CAAE,KAAM,CACnB,CAAC,cAGF3C,KAAA,QAAKkC,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7DrC,IAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACzC9B,KAAK,eACJP,IAAA,CAAAI,SAAA,EAAAiC,QAAA,CACGX,aAAa,CAACnB,KAAK,CAAC,cACnBL,KAAA,QAAKkC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDrC,IAAA,QAAKoC,SAAS,CAAC,cAAc,CAACU,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAV,QAAA,cACnErC,IAAA,SAAMgD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,eAER,EAAK,CAAC,CACJ3C,KAAK,CAACiB,MAAM,GAAK,EAAE,cACrBtB,KAAA,QAAKkC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDrC,IAAA,QAAKoC,SAAS,CAAC,cAAc,CAACU,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAV,QAAA,cACnErC,IAAA,SAAMgD,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oMAAoM,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClP,CAAC,iBAER,EAAK,CAAC,CACJ,IAAI,CACR,CACH,CACE,CAAC,cAENhD,KAAA,QAAKkC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EACzC9B,KAAK,CAACiB,MAAM,CAAC,KAChB,EAAK,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}