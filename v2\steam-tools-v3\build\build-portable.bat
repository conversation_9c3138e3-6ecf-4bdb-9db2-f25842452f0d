@echo off
REM Steam Tools v3 - Build Portable Executable

echo ========================================
echo Steam Tools v3 - Portable Build
echo ========================================
echo.

echo Building portable executable (no installer)...
echo.

REM Build frontend first
echo Step 1: Building React frontend...
cd /d "%~dp0\..\frontend"
npm run build
if errorlevel 1 (
    echo Frontend build failed!
    pause
    exit /b 1
)
echo Frontend build completed.
echo.

REM Build portable executable
echo Step 2: Building portable executable...
cd /d "%~dp0\..\electron"

REM Kill any running Steam Tools processes to avoid file locks
echo Stopping any running Steam Tools processes...
taskkill /F /IM "Steam Tools v3.exe" 2>nul || echo No Steam Tools processes running

REM Clean dist directory with retry logic
echo Cleaning previous build...
for /L %%i in (1,1,3) do (
    rmdir /s /q "..\dist" 2>nul && goto :build_success
    echo Attempt %%i failed, retrying in 2 seconds...
    timeout /t 2 >nul
)

:build_success
npm run build-dir
if errorlevel 1 (
    echo Portable build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED!
echo ========================================
echo.
echo Your portable executable is ready:
echo Location: dist\win-unpacked\Steam Tools v3.exe
echo.
echo This is a standalone executable that doesn't need installation.
echo Just copy the entire "win-unpacked" folder to run anywhere.
echo.

pause
