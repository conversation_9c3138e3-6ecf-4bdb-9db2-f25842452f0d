from types import ModuleType
from typing import Union, Dict

Buffer = Union[bytes, bytearray, memoryview]

digest_size: int

class HMAC(object):
    digest_size: int

    def __init__(self,
		 key: Buffer,
                 msg: Buffer,
		 digestmod: ModuleType) -> None: ...
    def update(self, msg: <PERSON>uffer) -> HMAC: ...
    def copy(self) -> HMAC: ...
    def digest(self) -> bytes: ...
    def hexdigest(self) -> str: ...
    def verify(self, mac_tag: Buffer) -> None: ...
    def hexverify(self, hex_mac_tag: str) -> None: ...


def new(key: Buffer,
        msg: Buffer = ...,
	digestmod: ModuleType = ...) -> HMAC: ...
