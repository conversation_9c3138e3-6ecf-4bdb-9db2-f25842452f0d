from typing import Union, Optional, Tuple

from Crypto.Math.Numbers import Integer

class EccPoint(object):
    curve: str
    def __init__(self,
                 x: Union[int, Integer],
                 y: Union[int, Integer],
                 curve: Optional[str] = ...) -> None: ...

    def set(self, point: EccPoint) -> EccPoint: ...
    def __eq__(self, point: object) -> bool: ...
    def __neg__(self) -> EccPoint: ...
    def copy(self) -> EccPoint: ...
    def is_point_at_infinity(self) -> bool: ...
    def point_at_infinity(self) -> EccPoint: ...
    @property
    def x(self) -> int: ...
    @property
    def y(self) -> int: ...
    @property
    def xy(self) -> Tuple[int, int]: ...
    def size_in_bytes(self) -> int: ...
    def size_in_bits(self) -> int: ...
    def double(self) -> EccPoint: ...
    def __iadd__(self, point: EccPoint) -> EccPoint: ...
    def __add__(self, point: EccPoint) -> EccPoint: ...
    def __imul__(self, scalar: int) -> EccPoint: ...
    def __mul__(self, scalar: int) -> EccPoint: ...


class EccXPoint(object):
    curve: str
    def __init__(self,
                 x: Union[int, Integer],
                 curve: Optional[str] = ...) -> None: ...
    def set(self, point: EccXPoint) -> EccXPoint: ...
    def __eq__(self, point: object) -> bool: ...
    def copy(self) -> EccXPoint: ...
    def is_point_at_infinity(self) -> bool: ...
    def point_at_infinity(self) -> EccXPoint: ...
    @property
    def x(self) -> int: ...
    def size_in_bytes(self) -> int: ...
    def size_in_bits(self) -> int: ...
    def __imul__(self, scalar: int) -> EccXPoint: ...
    def __mul__(self, scalar: int) -> EccXPoint: ...
    def __rmul__(self, left_hand: int) -> EccXPoint: ...
