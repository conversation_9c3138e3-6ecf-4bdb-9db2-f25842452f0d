{"ast": null, "code": "// Status indicator component with animated dot\nimport React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatusIndicator=_ref=>{let{status,message}=_ref;const getDotClasses=()=>{const baseClasses='status-dot';return`${baseClasses} ${status}`;};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:getDotClasses()}),/*#__PURE__*/_jsx(\"span\",{className:\"text-steam-foreground\",children:message})]});};export default StatusIndicator;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "StatusIndicator", "_ref", "status", "message", "getDotClasses", "baseClasses", "className", "children"], "sources": ["D:/SourceCode/SteamManifestUpdater/v2/steam-tools-v3/frontend/src/components/StatusIndicator.tsx"], "sourcesContent": ["// Status indicator component with animated dot\n\nimport React from 'react';\nimport { StatusIndicatorProps } from '../types';\n\nconst StatusIndicator: React.FC<StatusIndicatorProps> = ({\n  status,\n  message\n}) => {\n  const getDotClasses = () => {\n    const baseClasses = 'status-dot';\n    return `${baseClasses} ${status}`;\n  };\n\n\n\n  return (\n    <div className=\"flex items-center gap-2 text-sm\">\n      <div className={getDotClasses()} />\n      <span className=\"text-steam-foreground\">\n        {message}\n      </span>\n    </div>\n  );\n};\n\nexport default StatusIndicator;\n"], "mappings": "AAAA;AAEA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG1B,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAGlD,IAHmD,CACvDC,MAAM,CACNC,OACF,CAAC,CAAAF,IAAA,CACC,KAAM,CAAAG,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,WAAW,CAAG,YAAY,CAChC,MAAO,GAAGA,WAAW,IAAIH,MAAM,EAAE,CACnC,CAAC,CAID,mBACEH,KAAA,QAAKO,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CV,IAAA,QAAKS,SAAS,CAAEF,aAAa,CAAC,CAAE,CAAE,CAAC,cACnCP,IAAA,SAAMS,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCJ,OAAO,CACJ,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}