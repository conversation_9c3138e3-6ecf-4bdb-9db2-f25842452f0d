{"ast": null, "code": "// Zustand store for application state management\nimport{create}from'zustand';import apiService from'../services/api';import websocketService from'../services/websocket';// Generate unique ID for toasts\nconst generateId=()=>Math.random().toString(36).substr(2,9);export const useAppStore=create((set,get)=>({// Initial state\nsystemInfo:null,isLoading:false,licenseKey:'',licenseValid:false,appInfo:null,downloadProgress:null,isDownloading:false,toasts:[],// Actions\nsetLicenseKey:key=>{set({licenseKey:key,licenseValid:false,appInfo:null});},validateLicense:async()=>{const{licenseKey}=get();if(!licenseKey.trim()){get().addToast({type:'error',title:'Validation Error',message:'Please enter a license key'});return;}set({isLoading:true});try{const response=await apiService.validateLicense(licenseKey);if(response.is_valid&&response.app_info){set({licenseValid:true,appInfo:response.app_info,isLoading:false});get().addToast({type:'success',title:'License Valid',message:`Found app: ${response.app_info.app_name}`});}else{set({licenseValid:false,appInfo:null,isLoading:false});get().addToast({type:'error',title:'Invalid License',message:response.error_message||'License key is not valid'});}}catch(error){set({licenseValid:false,appInfo:null,isLoading:false});get().addToast({type:'error',title:'Validation Failed',message:error instanceof Error?error.message:'An error occurred'});}},startDownload:async()=>{const{licenseKey,appInfo,systemInfo}=get();if(!licenseKey||!appInfo){get().addToast({type:'error',title:'Download Error',message:'Please validate your license key first'});return;}if(!(systemInfo!==null&&systemInfo!==void 0&&systemInfo.steam_path)){// Try to detect Steam path first\ntry{const steamResponse=await apiService.detectSteamPath();if(!steamResponse.is_valid||!steamResponse.steam_path){get().addToast({type:'error',title:'Steam Not Found',message:'Steam installation not found. Please install Steam first.'});return;}// Update system info with detected Steam path\nawait get().updateSystemInfo();}catch(error){get().addToast({type:'error',title:'Steam Detection Failed',message:'Could not detect Steam installation'});return;}}// Check if Steam is running and offer to close it\nif(systemInfo!==null&&systemInfo!==void 0&&systemInfo.steam_running){const shouldClose=window.confirm('Steam is currently running. It needs to be closed for the installation to work properly.\\n\\nClose Steam now?');if(shouldClose){try{await apiService.closeSteamProcesses();get().addToast({type:'success',title:'Steam Closed',message:'Steam processes have been closed'});// Update system info\nawait get().updateSystemInfo();}catch(error){get().addToast({type:'error',title:'Failed to Close Steam',message:'Could not close Steam processes'});return;}}else{get().addToast({type:'warning',title:'Download Cancelled',message:'Please close Steam manually and try again'});return;}}set({isDownloading:true,downloadProgress:null});try{const response=await apiService.startDownload(licenseKey,appInfo,systemInfo.steam_path);if(response.success){get().addToast({type:'success',title:'Download Started',message:'File download has begun'});}else{throw new Error(response.message);}}catch(error){set({isDownloading:false,downloadProgress:null});get().addToast({type:'error',title:'Download Failed',message:error instanceof Error?error.message:'Failed to start download'});}},updateSystemInfo:async()=>{try{const systemInfo=await apiService.getSystemInfo();set({systemInfo});}catch(error){console.error('Failed to update system info:',error);}},setDownloadProgress:progress=>{set({downloadProgress:progress});// If download is completed or failed, update downloading state\nif((progress===null||progress===void 0?void 0:progress.status)==='completed'||(progress===null||progress===void 0?void 0:progress.status)==='error'){set({isDownloading:false});}},addToast:toast=>{const newToast={...toast,id:generateId(),duration:toast.duration||5000};set(state=>({toasts:[...state.toasts,newToast]}));// Auto-remove toast after duration\nsetTimeout(()=>{get().removeToast(newToast.id);},newToast.duration);},removeToast:id=>{set(state=>({toasts:state.toasts.filter(toast=>toast.id!==id)}));}}));// Initialize WebSocket connection and system info on store creation\nconst initializeApp=async()=>{const store=useAppStore.getState();// Connect WebSocket\nconst clientId=`client_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;websocketService.connect(clientId);// Listen for download progress updates\nwebsocketService.onMessage(message=>{if(message.type==='download_progress'){store.setDownloadProgress(message);}});// Load initial system info\nawait store.updateSystemInfo();};// Initialize when the module is loaded\ninitializeApp().catch(console.error);export default useAppStore;", "map": {"version": 3, "names": ["create", "apiService", "websocketService", "generateId", "Math", "random", "toString", "substr", "useAppStore", "set", "get", "systemInfo", "isLoading", "licenseKey", "licenseValid", "appInfo", "downloadProgress", "isDownloading", "toasts", "setLicenseKey", "key", "validateLicense", "trim", "addToast", "type", "title", "message", "response", "is_valid", "app_info", "app_name", "error_message", "error", "Error", "startDownload", "steam_path", "steamResponse", "detectSteamPath", "updateSystemInfo", "steam_running", "shouldClose", "window", "confirm", "closeSteamProcesses", "success", "getSystemInfo", "console", "setDownloadProgress", "progress", "status", "toast", "newToast", "id", "duration", "state", "setTimeout", "removeToast", "filter", "initializeApp", "store", "getState", "clientId", "Date", "now", "connect", "onMessage", "catch"], "sources": ["D:/SourceCode/SteamManifestUpdater/v2/steam-tools-v3/frontend/src/store/useAppStore.ts"], "sourcesContent": ["// Zustand store for application state management\n\nimport { create } from 'zustand';\nimport { AppState, DownloadProgress, ToastMessage } from '../types';\nimport apiService from '../services/api';\nimport websocketService from '../services/websocket';\n\n// Generate unique ID for toasts\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\nexport const useAppStore = create<AppState>((set, get) => ({\n  // Initial state\n  systemInfo: null,\n  isLoading: false,\n  licenseKey: '',\n  licenseValid: false,\n  appInfo: null,\n  downloadProgress: null,\n  isDownloading: false,\n  toasts: [],\n\n  // Actions\n  setLicenseKey: (key: string) => {\n    set({ licenseKey: key, licenseValid: false, appInfo: null });\n  },\n\n  validateLicense: async () => {\n    const { licenseKey } = get();\n    \n    if (!licenseKey.trim()) {\n      get().addToast({\n        type: 'error',\n        title: 'Validation Error',\n        message: 'Please enter a license key'\n      });\n      return;\n    }\n\n    set({ isLoading: true });\n\n    try {\n      const response = await apiService.validateLicense(licenseKey);\n      \n      if (response.is_valid && response.app_info) {\n        set({ \n          licenseValid: true, \n          appInfo: response.app_info,\n          isLoading: false \n        });\n        \n        get().addToast({\n          type: 'success',\n          title: 'License Valid',\n          message: `Found app: ${response.app_info.app_name}`\n        });\n      } else {\n        set({ \n          licenseValid: false, \n          appInfo: null,\n          isLoading: false \n        });\n        \n        get().addToast({\n          type: 'error',\n          title: 'Invalid License',\n          message: response.error_message || 'License key is not valid'\n        });\n      }\n    } catch (error) {\n      set({ \n        licenseValid: false, \n        appInfo: null,\n        isLoading: false \n      });\n      \n      get().addToast({\n        type: 'error',\n        title: 'Validation Failed',\n        message: error instanceof Error ? error.message : 'An error occurred'\n      });\n    }\n  },\n\n  startDownload: async () => {\n    const { licenseKey, appInfo, systemInfo } = get();\n    \n    if (!licenseKey || !appInfo) {\n      get().addToast({\n        type: 'error',\n        title: 'Download Error',\n        message: 'Please validate your license key first'\n      });\n      return;\n    }\n\n    if (!systemInfo?.steam_path) {\n      // Try to detect Steam path first\n      try {\n        const steamResponse = await apiService.detectSteamPath();\n        if (!steamResponse.is_valid || !steamResponse.steam_path) {\n          get().addToast({\n            type: 'error',\n            title: 'Steam Not Found',\n            message: 'Steam installation not found. Please install Steam first.'\n          });\n          return;\n        }\n        \n        // Update system info with detected Steam path\n        await get().updateSystemInfo();\n      } catch (error) {\n        get().addToast({\n          type: 'error',\n          title: 'Steam Detection Failed',\n          message: 'Could not detect Steam installation'\n        });\n        return;\n      }\n    }\n\n    // Check if Steam is running and offer to close it\n    if (systemInfo?.steam_running) {\n      const shouldClose = window.confirm(\n        'Steam is currently running. It needs to be closed for the installation to work properly.\\n\\nClose Steam now?'\n      );\n      \n      if (shouldClose) {\n        try {\n          await apiService.closeSteamProcesses();\n          get().addToast({\n            type: 'success',\n            title: 'Steam Closed',\n            message: 'Steam processes have been closed'\n          });\n          \n          // Update system info\n          await get().updateSystemInfo();\n        } catch (error) {\n          get().addToast({\n            type: 'error',\n            title: 'Failed to Close Steam',\n            message: 'Could not close Steam processes'\n          });\n          return;\n        }\n      } else {\n        get().addToast({\n          type: 'warning',\n          title: 'Download Cancelled',\n          message: 'Please close Steam manually and try again'\n        });\n        return;\n      }\n    }\n\n    set({ isDownloading: true, downloadProgress: null });\n\n    try {\n      const response = await apiService.startDownload(\n        licenseKey, \n        appInfo, \n        systemInfo!.steam_path!\n      );\n      \n      if (response.success) {\n        get().addToast({\n          type: 'success',\n          title: 'Download Started',\n          message: 'File download has begun'\n        });\n      } else {\n        throw new Error(response.message);\n      }\n    } catch (error) {\n      set({ isDownloading: false, downloadProgress: null });\n      \n      get().addToast({\n        type: 'error',\n        title: 'Download Failed',\n        message: error instanceof Error ? error.message : 'Failed to start download'\n      });\n    }\n  },\n\n  updateSystemInfo: async () => {\n    try {\n      const systemInfo = await apiService.getSystemInfo();\n      set({ systemInfo });\n    } catch (error) {\n      console.error('Failed to update system info:', error);\n    }\n  },\n\n  setDownloadProgress: (progress: DownloadProgress | null) => {\n    set({ downloadProgress: progress });\n    \n    // If download is completed or failed, update downloading state\n    if (progress?.status === 'completed' || progress?.status === 'error') {\n      set({ isDownloading: false });\n    }\n  },\n\n  addToast: (toast: Omit<ToastMessage, 'id'>) => {\n    const newToast: ToastMessage = {\n      ...toast,\n      id: generateId(),\n      duration: toast.duration || 5000\n    };\n    \n    set(state => ({\n      toasts: [...state.toasts, newToast]\n    }));\n\n    // Auto-remove toast after duration\n    setTimeout(() => {\n      get().removeToast(newToast.id);\n    }, newToast.duration);\n  },\n\n  removeToast: (id: string) => {\n    set(state => ({\n      toasts: state.toasts.filter(toast => toast.id !== id)\n    }));\n  }\n}));\n\n// Initialize WebSocket connection and system info on store creation\nconst initializeApp = async () => {\n  const store = useAppStore.getState();\n  \n  // Connect WebSocket\n  const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  websocketService.connect(clientId);\n  \n  // Listen for download progress updates\n  websocketService.onMessage((message) => {\n    if (message.type === 'download_progress') {\n      store.setDownloadProgress(message as DownloadProgress);\n    }\n  });\n  \n  // Load initial system info\n  await store.updateSystemInfo();\n};\n\n// Initialize when the module is loaded\ninitializeApp().catch(console.error);\n\nexport default useAppStore;\n"], "mappings": "AAAA;AAEA,OAASA,MAAM,KAAQ,SAAS,CAEhC,MAAO,CAAAC,UAAU,KAAM,iBAAiB,CACxC,MAAO,CAAAC,gBAAgB,KAAM,uBAAuB,CAEpD;AACA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAMC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,CAEhE,MAAO,MAAM,CAAAC,WAAW,CAAGR,MAAM,CAAW,CAACS,GAAG,CAAEC,GAAG,IAAM,CACzD;AACAC,UAAU,CAAE,IAAI,CAChBC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,EAAE,CACdC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,IAAI,CACbC,gBAAgB,CAAE,IAAI,CACtBC,aAAa,CAAE,KAAK,CACpBC,MAAM,CAAE,EAAE,CAEV;AACAC,aAAa,CAAGC,GAAW,EAAK,CAC9BX,GAAG,CAAC,CAAEI,UAAU,CAAEO,GAAG,CAAEN,YAAY,CAAE,KAAK,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAC,CAC9D,CAAC,CAEDM,eAAe,CAAE,KAAAA,CAAA,GAAY,CAC3B,KAAM,CAAER,UAAW,CAAC,CAAGH,GAAG,CAAC,CAAC,CAE5B,GAAI,CAACG,UAAU,CAACS,IAAI,CAAC,CAAC,CAAE,CACtBZ,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,kBAAkB,CACzBC,OAAO,CAAE,4BACX,CAAC,CAAC,CACF,OACF,CAEAjB,GAAG,CAAC,CAAEG,SAAS,CAAE,IAAK,CAAC,CAAC,CAExB,GAAI,CACF,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA1B,UAAU,CAACoB,eAAe,CAACR,UAAU,CAAC,CAE7D,GAAIc,QAAQ,CAACC,QAAQ,EAAID,QAAQ,CAACE,QAAQ,CAAE,CAC1CpB,GAAG,CAAC,CACFK,YAAY,CAAE,IAAI,CAClBC,OAAO,CAAEY,QAAQ,CAACE,QAAQ,CAC1BjB,SAAS,CAAE,KACb,CAAC,CAAC,CAEFF,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,eAAe,CACtBC,OAAO,CAAE,cAAcC,QAAQ,CAACE,QAAQ,CAACC,QAAQ,EACnD,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrB,GAAG,CAAC,CACFK,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,IAAI,CACbH,SAAS,CAAE,KACb,CAAC,CAAC,CAEFF,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,iBAAiB,CACxBC,OAAO,CAAEC,QAAQ,CAACI,aAAa,EAAI,0BACrC,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,KAAK,CAAE,CACdvB,GAAG,CAAC,CACFK,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,IAAI,CACbH,SAAS,CAAE,KACb,CAAC,CAAC,CAEFF,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,mBAAmB,CAC1BC,OAAO,CAAEM,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACN,OAAO,CAAG,mBACpD,CAAC,CAAC,CACJ,CACF,CAAC,CAEDQ,aAAa,CAAE,KAAAA,CAAA,GAAY,CACzB,KAAM,CAAErB,UAAU,CAAEE,OAAO,CAAEJ,UAAW,CAAC,CAAGD,GAAG,CAAC,CAAC,CAEjD,GAAI,CAACG,UAAU,EAAI,CAACE,OAAO,CAAE,CAC3BL,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,gBAAgB,CACvBC,OAAO,CAAE,wCACX,CAAC,CAAC,CACF,OACF,CAEA,GAAI,EAACf,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEwB,UAAU,EAAE,CAC3B;AACA,GAAI,CACF,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAnC,UAAU,CAACoC,eAAe,CAAC,CAAC,CACxD,GAAI,CAACD,aAAa,CAACR,QAAQ,EAAI,CAACQ,aAAa,CAACD,UAAU,CAAE,CACxDzB,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,iBAAiB,CACxBC,OAAO,CAAE,2DACX,CAAC,CAAC,CACF,OACF,CAEA;AACA,KAAM,CAAAhB,GAAG,CAAC,CAAC,CAAC4B,gBAAgB,CAAC,CAAC,CAChC,CAAE,MAAON,KAAK,CAAE,CACdtB,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,wBAAwB,CAC/BC,OAAO,CAAE,qCACX,CAAC,CAAC,CACF,OACF,CACF,CAEA;AACA,GAAIf,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAE4B,aAAa,CAAE,CAC7B,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACC,OAAO,CAChC,8GACF,CAAC,CAED,GAAIF,WAAW,CAAE,CACf,GAAI,CACF,KAAM,CAAAvC,UAAU,CAAC0C,mBAAmB,CAAC,CAAC,CACtCjC,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,cAAc,CACrBC,OAAO,CAAE,kCACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAAhB,GAAG,CAAC,CAAC,CAAC4B,gBAAgB,CAAC,CAAC,CAChC,CAAE,MAAON,KAAK,CAAE,CACdtB,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,uBAAuB,CAC9BC,OAAO,CAAE,iCACX,CAAC,CAAC,CACF,OACF,CACF,CAAC,IAAM,CACLhB,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,oBAAoB,CAC3BC,OAAO,CAAE,2CACX,CAAC,CAAC,CACF,OACF,CACF,CAEAjB,GAAG,CAAC,CAAEQ,aAAa,CAAE,IAAI,CAAED,gBAAgB,CAAE,IAAK,CAAC,CAAC,CAEpD,GAAI,CACF,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAA1B,UAAU,CAACiC,aAAa,CAC7CrB,UAAU,CACVE,OAAO,CACPJ,UAAU,CAAEwB,UACd,CAAC,CAED,GAAIR,QAAQ,CAACiB,OAAO,CAAE,CACpBlC,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,kBAAkB,CACzBC,OAAO,CAAE,yBACX,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,KAAM,IAAI,CAAAO,KAAK,CAACN,QAAQ,CAACD,OAAO,CAAC,CACnC,CACF,CAAE,MAAOM,KAAK,CAAE,CACdvB,GAAG,CAAC,CAAEQ,aAAa,CAAE,KAAK,CAAED,gBAAgB,CAAE,IAAK,CAAC,CAAC,CAErDN,GAAG,CAAC,CAAC,CAACa,QAAQ,CAAC,CACbC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,iBAAiB,CACxBC,OAAO,CAAEM,KAAK,WAAY,CAAAC,KAAK,CAAGD,KAAK,CAACN,OAAO,CAAG,0BACpD,CAAC,CAAC,CACJ,CACF,CAAC,CAEDY,gBAAgB,CAAE,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF,KAAM,CAAA3B,UAAU,CAAG,KAAM,CAAAV,UAAU,CAAC4C,aAAa,CAAC,CAAC,CACnDpC,GAAG,CAAC,CAAEE,UAAW,CAAC,CAAC,CACrB,CAAE,MAAOqB,KAAK,CAAE,CACdc,OAAO,CAACd,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAAC,CAEDe,mBAAmB,CAAGC,QAAiC,EAAK,CAC1DvC,GAAG,CAAC,CAAEO,gBAAgB,CAAEgC,QAAS,CAAC,CAAC,CAEnC;AACA,GAAI,CAAAA,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEC,MAAM,IAAK,WAAW,EAAI,CAAAD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEC,MAAM,IAAK,OAAO,CAAE,CACpExC,GAAG,CAAC,CAAEQ,aAAa,CAAE,KAAM,CAAC,CAAC,CAC/B,CACF,CAAC,CAEDM,QAAQ,CAAG2B,KAA+B,EAAK,CAC7C,KAAM,CAAAC,QAAsB,CAAG,CAC7B,GAAGD,KAAK,CACRE,EAAE,CAAEjD,UAAU,CAAC,CAAC,CAChBkD,QAAQ,CAAEH,KAAK,CAACG,QAAQ,EAAI,IAC9B,CAAC,CAED5C,GAAG,CAAC6C,KAAK,GAAK,CACZpC,MAAM,CAAE,CAAC,GAAGoC,KAAK,CAACpC,MAAM,CAAEiC,QAAQ,CACpC,CAAC,CAAC,CAAC,CAEH;AACAI,UAAU,CAAC,IAAM,CACf7C,GAAG,CAAC,CAAC,CAAC8C,WAAW,CAACL,QAAQ,CAACC,EAAE,CAAC,CAChC,CAAC,CAAED,QAAQ,CAACE,QAAQ,CAAC,CACvB,CAAC,CAEDG,WAAW,CAAGJ,EAAU,EAAK,CAC3B3C,GAAG,CAAC6C,KAAK,GAAK,CACZpC,MAAM,CAAEoC,KAAK,CAACpC,MAAM,CAACuC,MAAM,CAACP,KAAK,EAAIA,KAAK,CAACE,EAAE,GAAKA,EAAE,CACtD,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAM,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,KAAM,CAAAC,KAAK,CAAGnD,WAAW,CAACoD,QAAQ,CAAC,CAAC,CAEpC;AACA,KAAM,CAAAC,QAAQ,CAAG,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI3D,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,CAAE,CAAC,CAAC,EAAE,CAClFL,gBAAgB,CAAC8D,OAAO,CAACH,QAAQ,CAAC,CAElC;AACA3D,gBAAgB,CAAC+D,SAAS,CAAEvC,OAAO,EAAK,CACtC,GAAIA,OAAO,CAACF,IAAI,GAAK,mBAAmB,CAAE,CACxCmC,KAAK,CAACZ,mBAAmB,CAACrB,OAA2B,CAAC,CACxD,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAiC,KAAK,CAACrB,gBAAgB,CAAC,CAAC,CAChC,CAAC,CAED;AACAoB,aAAa,CAAC,CAAC,CAACQ,KAAK,CAACpB,OAAO,CAACd,KAAK,CAAC,CAEpC,cAAe,CAAAxB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}