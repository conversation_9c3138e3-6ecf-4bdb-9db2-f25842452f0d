[{"D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\index.tsx": "1", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\App-simple.tsx": "2", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\App.tsx": "3", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\store\\useAppStore.ts": "4", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\StatusIndicator.tsx": "5", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\ActivateButton.tsx": "6", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\ProgressBar.tsx": "7", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\LicenseInput.tsx": "8", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\Toast.tsx": "9", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\services\\api.ts": "10", "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\services\\websocket.ts": "11"}, {"size": 274, "mtime": 1754374407794, "results": "12", "hashOfConfig": "13"}, {"size": 412, "mtime": 1754373874953, "results": "14", "hashOfConfig": "13"}, {"size": 6392, "mtime": 1754374445744, "results": "15", "hashOfConfig": "13"}, {"size": 6611, "mtime": 1754375429317, "results": "16", "hashOfConfig": "13"}, {"size": 568, "mtime": 1754375418786, "results": "17", "hashOfConfig": "13"}, {"size": 1745, "mtime": 1754369410116, "results": "18", "hashOfConfig": "13"}, {"size": 1280, "mtime": 1754369419160, "results": "19", "hashOfConfig": "13"}, {"size": 3563, "mtime": 1754375408038, "results": "20", "hashOfConfig": "13"}, {"size": 2305, "mtime": 1754369441217, "results": "21", "hashOfConfig": "13"}, {"size": 2966, "mtime": 1754369333129, "results": "22", "hashOfConfig": "13"}, {"size": 3909, "mtime": 1754369350915, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1w00ptt", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\index.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\App-simple.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\App.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\store\\useAppStore.ts", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\StatusIndicator.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\ActivateButton.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\ProgressBar.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\LicenseInput.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\components\\Toast.tsx", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\services\\api.ts", [], [], "D:\\SourceCode\\SteamManifestUpdater\\v2\\steam-tools-v3\\frontend\\src\\services\\websocket.ts", [], []]