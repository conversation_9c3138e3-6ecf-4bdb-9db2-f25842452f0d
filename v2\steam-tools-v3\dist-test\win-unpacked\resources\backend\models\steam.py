"""Steam-related data models."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class LicenseKeyRequest(BaseModel):
    """Request model for license key validation."""
    license_key: str = Field(..., min_length=1, max_length=100)


class AppInfo(BaseModel):
    """Application information from KeyAuth."""
    app_id: str
    app_name: str
    license_prefix: str
    download_urls: Optional[Dict[str, str]] = None


class LicenseValidationResponse(BaseModel):
    """Response model for license validation."""
    is_valid: bool
    app_info: Optional[AppInfo] = None
    error_message: Optional[str] = None


class SteamPathRequest(BaseModel):
    """Request model for Steam path detection."""
    auto_detect: bool = True
    custom_path: Optional[str] = None


class SteamPathResponse(BaseModel):
    """Response model for Steam path detection."""
    steam_path: Optional[str]
    is_valid: bool
    error_message: Optional[str] = None


class DownloadFileInfo(BaseModel):
    """Information about a file to download."""
    url: str
    path: str
    name: str
    size: Optional[int] = None


class DownloadRequest(BaseModel):
    """Request model for file downloads."""
    license_key: str
    app_info: AppInfo
    steam_path: str


class DownloadProgress(BaseModel):
    """Progress information for downloads."""
    file_name: str
    current_file: int
    total_files: int
    bytes_downloaded: int
    total_bytes: int
    percentage: float
    status: str
    error_message: Optional[str] = None


class SystemInfo(BaseModel):
    """System information model."""
    is_admin: bool
    steam_running: bool
    steam_path: Optional[str]
    platform: str
    python_version: str


class ConfigData(BaseModel):
    """Configuration data model."""
    steam_path: str = ""
    license_key_history: List[Dict[str, Any]] = Field(default_factory=list)
    admin_settings: Dict[str, Any] = Field(default_factory=dict)
    app_cache: Dict[str, Any] = Field(default_factory=dict)
    last_used: Dict[str, Any] = Field(default_factory=dict)


class LicenseHistoryEntry(BaseModel):
    """License key history entry."""
    key: str
    app_name: str
    app_id: str
    license_prefix: str
    timestamp: datetime
    success: bool


class AdminRequest(BaseModel):
    """Admin authentication request."""
    password: str


class AdminResponse(BaseModel):
    """Admin authentication response."""
    authenticated: bool
    error_message: Optional[str] = None


class LogEntry(BaseModel):
    """Log entry model."""
    timestamp: datetime
    level: str
    category: str
    message: str
    details: Dict[str, Any] = Field(default_factory=dict)


class StatusResponse(BaseModel):
    """General status response."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
